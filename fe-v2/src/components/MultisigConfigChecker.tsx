import React, { useEffect } from 'react';
import { Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';

const MultisigConfigChecker: React.FC = () => {
  const { hasMultisigAddress, loading } = useModel('multisig');

  useEffect(() => {
    // 等待数据加载完成后检查
    if (!loading && !hasMultisigAddress) {
      Modal.warning({
        title: '多签地址未配置',
        icon: <ExclamationCircleOutlined />,
        content: (
          <div>
            <p>系统检测到后端未配置多签地址，部分功能可能无法正常使用。</p>
            <p>请联系管理员在后端 <code>be-v2/utils.js</code> 文件中配置 <code>FIXED_MULTISIG_ADDRESS</code> 变量。</p>
          </div>
        ),
        okText: '我知道了',
        width: 500,
      });
    }
  }, [hasMultisigAddress, loading]);

  return null; // 这个组件不渲染任何内容，只负责检查和提示
};

export default MultisigConfigChecker;

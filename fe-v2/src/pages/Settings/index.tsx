import React from 'react'
import { Card, Descriptions, Space, message, Alert } from 'antd'
import { useModel } from '@umijs/max'
import AddressCopy from '@/components/AddressCopy'

const Settings: React.FC = () => {
  const {
    currentMultisig: multisig,
    loading,
    hasMultisigAddress
  } = useModel('multisig')

  const [messageApi, contextHolder] = message.useMessage()

  return (
    <>
      {contextHolder}
      <div>
        {/* 多签地址未配置提示 */}
        {!hasMultisigAddress && (
          <Card title="多签地址配置" style={{ marginBottom: 16 }}>
            <Alert
              message="多签地址未配置"
              description="多签地址需要在后端配置。请联系管理员在后端 utils.js 文件中配置 FIXED_MULTISIG_ADDRESS 变量。"
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          </Card>
        )}

        {/* 多签信息展示 */}
        {hasMultisigAddress && multisig && (
          <Card title="多签信息" loading={loading}>
            <Descriptions bordered>
              <Descriptions.Item label="Members" span={3}>
                <div>{multisig?.members.length}</div>
              </Descriptions.Item>
              <Descriptions.Item label="Threshold" span={3}>
                <Space>{multisig?.threshold}/{multisig?.members?.length ? multisig?.members?.length - 1 : 0}</Space>
              </Descriptions.Item>
              <Descriptions.Item label="Squad Vault" span={3}>
                <AddressCopy address={multisig?.vault.address || ''} showFullAddress={true} />
              </Descriptions.Item>
              <Descriptions.Item label="Multisig Account" span={3}>
                <AddressCopy address={multisig?.address || ''} showFullAddress={true} />
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}
      </div>
    </>
  )
}

export default Settings

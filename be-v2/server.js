import Koa from 'koa'
import bodyParser from 'koa-bodyparser'
import { router } from './router.js'
import { PORT } from './constants.js'

const app = new Koa()

app.use(bodyParser())
app.use(router.routes())
app.use(router.allowedMethods())

async function run () {
  // ubikey检测
  // try {
  //   execSync('gpg --card-status')
  // } catch (err) {
  //   console.log('yubikey设备未连接')
  //   process.exit(1)
  // }

  // 下载并解密文件
  // await downloadFile('tos.bin')
  // await downloadFile('tokens.bin')

  // 解密并加载数据
  // await loadTokens()
  // await loadTos()

  app.listen(PORT, () => console.log(`🚀 Squads Backend Server running on http://${SERVER_HOST}:${SERVER_PORT}`))
}

run()
